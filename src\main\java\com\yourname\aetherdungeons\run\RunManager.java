package com.yourname.aetherdungeons.run;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.dungeon.world.InstanceWorldManager;
import com.yourname.aetherdungeons.party.Party;
import com.yourname.aetherdungeons.party.PartyManager;
import com.yourname.aetherdungeons.spawn.bosses.BossSpawner;
import com.yourname.aetherdungeons.spawn.bosses.BossStorage;
import com.yourname.aetherdungeons.spawn.chests.LootStorage;
import com.yourname.aetherdungeons.spawn.mobs.MobSpawner;
import com.yourname.aetherdungeons.spawn.mobs.MobStorage;
import net.kyori.adventure.text.Component;
import org.bukkit.*;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class RunManager {
    private final AetherDungeonsPlugin plugin;
    private final DungeonStorage dungeonStorage;
    private final InstanceWorldManager instanceWorldManager;
    private final PartyManager partyManager;
    private final Leaderboards leaderboards;
    private final MobStorage mobStorage;
    private final BossStorage bossStorage;
    private final LootStorage lootStorage;

    private final Map<String, RunState> runsByWorld = new ConcurrentHashMap<>();

    public RunManager(AetherDungeonsPlugin plugin, DungeonStorage dungeonStorage, InstanceWorldManager instanceWorldManager, PartyManager partyManager, Leaderboards leaderboards, MobStorage mobStorage, BossStorage bossStorage, LootStorage lootStorage) {
        this.plugin = plugin;
        this.dungeonStorage = dungeonStorage;
        this.instanceWorldManager = instanceWorldManager;
        this.partyManager = partyManager;
        this.leaderboards = leaderboards;
        this.mobStorage = mobStorage;
        this.bossStorage = bossStorage;
        this.lootStorage = lootStorage;
    }

    public DungeonStorage getDungeonStorage() { return dungeonStorage; }

    public boolean isInstanceWorld(World w) {
        return instanceWorldManager.isInstanceWorld(w);
    }

    public void tryStartRun(Player initiator, Dungeon dungeon, boolean adminOverride) {
        Party party = partyManager.getPartyOf(initiator);
        if (party == null) {
            initiator.sendMessage(Component.text("Create a party first in Parties menu."));
            return;
        }
        if (!adminOverride && !party.isOwner(initiator.getUniqueId())) {
            initiator.sendMessage(Component.text("Only the party owner can start a run."));
            return;
        }
        if (!adminOverride && !party.allReady()) {
            initiator.sendMessage(Component.text("All party members must be ready."));
            return;
        }
        if (!dungeon.isReadyForRun()) {
            initiator.sendMessage(Component.text("Dungeon is not ready: set start/end blocks."));
            return;
        }
        int size = party.getMemberIds().size();
        if (size > dungeon.getRules().getMaxPlayers()) {
            initiator.sendMessage(Component.text("Party too large for this dungeon."));
            return;
        }
        long ts = System.currentTimeMillis();
        initiator.sendMessage(Component.text("Preparing instance..."));
        instanceWorldManager.createInstanceFromAuthor(dungeon, ts).thenAccept(world -> {
            if (world == null) {
                initiator.sendMessage(Component.text("Failed to create instance."));
                return;
            }
            World w = world;
            RunState state = new RunState(dungeon, w, System.currentTimeMillis());
            runsByWorld.put(w.getName(), state);

            Location start = dungeon.getMarkers().getStart();
            Location startInInstance = new Location(w, start.getX(), start.getY(), start.getZ(), start.getYaw(), start.getPitch());
            for (var member : party.getMembers()) {
                state.getPlayers().add(member.getUniqueId());
                Bukkit.getScheduler().runTask(plugin, () -> {
                    member.teleport(startInInstance);
                    member.sendMessage(Component.text("Run started: " + dungeon.getName()));
                    executeCommand(dungeon.getStartCommand(), member, dungeon, w.getName());
                });
            }

            // Spawn mobs/bosses at anchors
            MobSpawner.spawnAll(state, mobStorage);
            BossSpawner.spawnAll(state, bossStorage);

            // Timer to enforce time limit
            Bukkit.getScheduler().runTaskTimer(plugin, task -> {
                if (!runsByWorld.containsKey(w.getName())) {
                    task.cancel();
                    return;
                }
                long elapsed = System.currentTimeMillis() - state.getStartMillis();
                if (elapsed >= dungeon.getRules().getTimeLimitSeconds() * 1000L) {
                    broadcast(state, "Time is up!");
                    finishRun(state, false);
                    task.cancel();
                }
            }, 20L, 20L);
        }).exceptionally(ex -> {
            initiator.sendMessage(Component.text("Error: " + ex.getMessage()));
            return null;
        });
    }

    private void broadcast(RunState state, String msg) {
        for (UUID id : state.getPlayers()) {
            Player p = Bukkit.getPlayer(id);
            if (p != null && p.getWorld().equals(state.getWorld())) p.sendMessage(Component.text(msg));
        }
    }

    public void finishRun(RunState state, boolean success) {
        long time = System.currentTimeMillis() - state.getStartMillis();
        if (success) {
            for (UUID id : state.getPlayers()) {
                Player p = Bukkit.getPlayer(id);
                if (p != null) {
                    leaderboards.addRecord(state.getDungeon().getId(), p.getName(), time);
                    executeCommand(state.getDungeon().getExitCommand(), p, state.getDungeon(), state.getWorld().getName());
                    p.teleport(p.getWorld().getSpawnLocation());
                }
            }
        } else {
            for (UUID id : state.getPlayers()) {
                Player p = Bukkit.getPlayer(id);
                if (p != null) {
                    p.sendMessage(Component.text("Run ended."));
                    p.teleport(p.getWorld().getSpawnLocation());
                }
            }
        }
        runsByWorld.remove(state.getWorld().getName());
        instanceWorldManager.unloadAndDeleteWorld(state.getWorld());
    }

    public void shutdownAllRunsSync() {
        for (RunState state : new ArrayList<>(runsByWorld.values())) {
            try {
                finishRun(state, false);
            } catch (Exception ignored) {}
        }
    }

    public void exitPlayer(Player p) {
        World w = p.getWorld();
        RunState state = runsByWorld.get(w.getName());
        if (state == null) {
            p.sendMessage(Component.text("You're not in a run."));
            return;
        }
        p.teleport(p.getWorld().getSpawnLocation());
        state.getPlayers().remove(p.getUniqueId());
        if (state.getPlayers().isEmpty()) {
            finishRun(state, false);
        }
    }

    public boolean isRunWorld(World world) {
        return world != null && runsByWorld.containsKey(world.getName());
    }

    public RunState getStateByWorld(World world) {
        return runsByWorld.get(world.getName());
    }

    private void executeCommand(String command, Player player, Dungeon dungeon, String runId) {
        if (command == null || command.isBlank()) return;
        String parsed = command
                .replace("{player}", player.getName())
                .replace("{dungeon}", dungeon.getId())
                .replace("{runId}", runId);
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), parsed);
    }
}
