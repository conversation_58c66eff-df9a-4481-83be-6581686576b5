package com.yourname.aetherdungeons.spawn.mobs;

import com.yourname.aetherdungeons.run.RunState;
import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.Mob;
import org.bukkit.entity.Player;

public class MobSpawner {
    public static void spawnAll(RunState state, MobStorage storage) {
        var dungeon = state.getDungeon();
        var world = state.getWorld();
        for (int i = 0; i < dungeon.getMobAnchors().size(); i++) {
            Location loc = dungeon.getMobAnchors().get(i);
            String id = dungeon.getMobTemplates().get(i);
            MobTemplate t = storage.get(id);
            if (t == null) continue;
            Location in = new Location(world, loc.getX(), loc.getY(), loc.getZ());
            var entity = world.spawnEntity(in, t.getType());
            if (entity instanceof Mob mob) {
                if (t.getName() != null) mob.customName(net.kyori.adventure.text.Component.text(t.getName()));
                var attr = mob.getAttribute(Attribute.GENERIC_MAX_HEALTH);
                if (attr != null) attr.setBaseValue(t.getMaxHealth());
                mob.setHealth(Math.max(1.0, t.getMaxHealth()));
                var spd = mob.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED);
                if (spd != null) spd.setBaseValue(t.getSpeed());
                var dmg = mob.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE);
                if (dmg != null) dmg.setBaseValue(t.getDamage());
            }
        }
    }
}
