package com.yourname.aetherdungeons.dungeon.model;

public class Rules {
    private int maxPlayers = 5;
    private int timeLimitSeconds = 1800;
    private boolean deathsSpectate = true;
    private int livesPerPlayer = 1;
    private boolean scalingEnabled = true;
    private int hpPerPlayerPct = 15;
    private int dmgPerPlayerPct = 10;

    public int getMaxPlayers() { return maxPlayers; }
    public void setMaxPlayers(int maxPlayers) { this.maxPlayers = maxPlayers; }
    public int getTimeLimitSeconds() { return timeLimitSeconds; }
    public void setTimeLimitSeconds(int timeLimitSeconds) { this.timeLimitSeconds = timeLimitSeconds; }
    public boolean isDeathsSpectate() { return deathsSpectate; }
    public void setDeathsSpectate(boolean deathsSpectate) { this.deathsSpectate = deathsSpectate; }
    public int getLivesPerPlayer() { return livesPerPlayer; }
    public void setLivesPerPlayer(int livesPerPlayer) { this.livesPerPlayer = livesPerPlayer; }
    public boolean isScalingEnabled() { return scalingEnabled; }
    public void setScalingEnabled(boolean scalingEnabled) { this.scalingEnabled = scalingEnabled; }
    public int getHpPerPlayerPct() { return hpPerPlayerPct; }
    public void setHpPerPlayerPct(int hpPerPlayerPct) { this.hpPerPlayerPct = hpPerPlayerPct; }
    public int getDmgPerPlayerPct() { return dmgPerPlayerPct; }
    public void setDmgPerPlayerPct(int dmgPerPlayerPct) { this.dmgPerPlayerPct = dmgPerPlayerPct; }
}
