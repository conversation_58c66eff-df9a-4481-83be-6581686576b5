package com.yourname.aetherdungeons.dungeon.model;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;

import java.util.ArrayList;
import java.util.List;

public class Dungeon {
    private String id;
    private String name;
    private WorldProfile worldProfile = WorldProfile.VOID;
    private final StartEndMarkers markers = new StartEndMarkers();

    private String startCommand = "";
    private String exitCommand = "";

    // Assignments
    private final List<Location> chestLocations = new ArrayList<>();
    private final List<String> chestLootTables = new ArrayList<>();
    private final List<Location> mobAnchors = new ArrayList<>();
    private final List<String> mobTemplates = new ArrayList<>();
    private final List<Location> bossAnchors = new ArrayList<>();
    private final List<String> bossTemplates = new ArrayList<>();

    private final Rules rules = new Rules();

    public Dungeon(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String authorWorldName() {
        return "dungeon_" + id + "_author";
    }

    public String instanceWorldName(long timestamp) {
        return "dungeon_" + id + "_run_" + timestamp;
    }

    public String getId() { return id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public WorldProfile getWorldProfile() { return worldProfile; }
    public void setWorldProfile(WorldProfile worldProfile) { this.worldProfile = worldProfile; }
    public StartEndMarkers getMarkers() { return markers; }
    public Rules getRules() { return rules; }
    public String getStartCommand() { return startCommand; }
    public void setStartCommand(String startCommand) { this.startCommand = startCommand; }
    public String getExitCommand() { return exitCommand; }
    public void setExitCommand(String exitCommand) { this.exitCommand = exitCommand; }

    // Assignments simple parallel lists
    public void addChest(Location loc, String lootTable) {
        chestLocations.add(loc);
        chestLootTables.add(lootTable);
    }
    public List<Location> getChestLocations() { return chestLocations; }
    public List<String> getChestLootTables() { return chestLootTables; }

    public void addMob(Location loc, String template) {
        mobAnchors.add(loc);
        mobTemplates.add(template);
    }
    public List<Location> getMobAnchors() { return mobAnchors; }
    public List<String> getMobTemplates() { return mobTemplates; }

    public void addBoss(Location loc, String template) {
        bossAnchors.add(loc);
        bossTemplates.add(template);
    }
    public List<Location> getBossAnchors() { return bossAnchors; }
    public List<String> getBossTemplates() { return bossTemplates; }

    public boolean isReadyForRun() {
        if (markers.getStart() == null || markers.getEnd() == null) return false;
        if (markers.getStart().getWorld() == null || markers.getEnd().getWorld() == null) return false;
        return true;
    }

    public World getAuthorWorld() {
        return Bukkit.getWorld(authorWorldName());
    }

    public String markersSummary() {
        Location s = markers.getStart();
        Location e = markers.getEnd();
        return "Start=" + (s == null ? "unset" : s.getBlockX()+","+s.getBlockY()+","+s.getBlockZ()+"("+markers.getStartType()+")")
                + " End=" + (e == null ? "unset" : e.getBlockX()+","+e.getBlockY()+","+e.getBlockZ()+"("+markers.getEndType()+")");
    }
}
