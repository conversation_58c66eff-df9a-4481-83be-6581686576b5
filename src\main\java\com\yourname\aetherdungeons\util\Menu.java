package com.yourname.aetherdungeons.util;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;

import java.util.function.Consumer;

public abstract class Menu implements InventoryHolder {

    protected final Inventory inv;

    public Menu(String title, int size) {
        this.inv = Bukkit.createInventory(this, size, net.kyori.adventure.text.Component.text(title));
    }

    public void open(Player player) {
        build();
        player.openInventory(inv);
    }

    protected abstract void build();

    @Override
    public Inventory getInventory() {
        return inv;
    }

    public void onClick(InventoryClickEvent e) {
        // Default no-op; override in subclasses
    }

    protected void fillBorders(org.bukkit.Material material) {
        int size = inv.getSize();
        for (int i = 0; i < size; i++) {
            boolean border = i < 9 || i >= size - 9 || i % 9 == 0 || i % 9 == 8;
            if (border && inv.getItem(i) == null) {
                inv.setItem(i, new ItemBuilder(material).name(" ").build());
            }
        }
    }
}
