package com.yourname.aetherdungeons.util;

import com.yourname.aetherdungeons.dungeon.world.AuthorWorldManager;
import com.yourname.aetherdungeons.run.RunManager;
import org.bukkit.World;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerJoinEvent;

public class ProtectionListener implements Listener {

    private final RunManager runManager;
    private final AuthorWorldManager authorWorldManager;

    public ProtectionListener(RunManager runManager, AuthorWorldManager authorWorldManager) {
        this.runManager = runManager;
        this.authorWorldManager = authorWorldManager;
    }

    @EventHandler
    public void onPlace(BlockPlaceEvent e) {
        World w = e.getBlock().getWorld();
        if (runManager.isInstanceWorld(w)) {
            if (!e.getPlayer().hasPermission("aetherdungeons.admin.*")) {
                e.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onBreak(BlockBreakEvent e) {
        World w = e.getBlock().getWorld();
        if (runManager.isInstanceWorld(w)) {
            if (!e.getPlayer().hasPermission("aetherdungeons.admin.*")) {
                e.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onDamage(EntityDamageEvent e) {
        World w = e.getEntity().getWorld();
        if (authorWorldManager.isAuthorWorld(w)) {
            if (e.getEntity() instanceof org.bukkit.entity.Player) {
                e.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onSpawn(CreatureSpawnEvent e) {
        World w = e.getLocation().getWorld();
        if (authorWorldManager.isAuthorWorld(w)) {
            e.setCancelled(true);
        }
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent e) {
        // Could show hints
    }
}
