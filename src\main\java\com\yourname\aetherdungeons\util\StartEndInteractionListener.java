package com.yourname.aetherdungeons.util;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.run.RunManager;
import com.yourname.aetherdungeons.run.RunState;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;

public class StartEndInteractionListener implements Listener {

    private final AetherDungeonsPlugin plugin;

    public StartEndInteractionListener(AetherDungeonsPlugin plugin) {
        this.plugin = plugin;
        org.bukkit.Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    @EventHandler
    public void onInteract(PlayerInteractEvent e) {
        if (e.getClickedBlock() == null) return;
        Player p = e.getPlayer();
        Block b = e.getClickedBlock();
        World w = b.getWorld();
        RunManager rm = plugin.getRunManager();
        if (!rm.isRunWorld(w)) return;
        RunState state = rm.getStateByWorld(w);
        if (state == null) return;
        // Check end block
        var end = state.getDungeon().getMarkers().getEnd();
        if (end != null && b.getX() == end.getBlockX() && b.getY() == end.getBlockY() && b.getZ() == end.getBlockZ() && b.getType() == state.getDungeon().getMarkers().getEndType()) {
            rm.finishRun(state, true);
        }
    }
}
