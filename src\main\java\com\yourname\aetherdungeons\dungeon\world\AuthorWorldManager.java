package com.yourname.aetherdungeons.dungeon.world;

import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.model.WorldProfile;
import org.bukkit.Bukkit;
import org.bukkit.GameRule;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.generator.ChunkGenerator;
import org.bukkit.plugin.Plugin;

import java.util.Random;

public class AuthorWorldManager {
    private final Plugin plugin;

    public AuthorWorldManager(Plugin plugin) {
        this.plugin = plugin;
    }

    public World ensureAuthorWorld(Dungeon dungeon) {
        World w = Bukkit.getWorld(dungeon.authorWorldName());
        if (w != null) return w;

        WorldCreator creator = new WorldCreator(dungeon.authorWorldName());
        if (dungeon.getWorldProfile() == WorldProfile.VOID) {
            creator.generator(new VoidChunkGenerator());
        } else {
            creator.type(org.bukkit.WorldType.FLAT);
            creator.generatorSettings("minecraft:bedrock,2*minecraft:dirt,minecraft:grass_block;0;"); // minimal superflat
        }
        w = Bukkit.createWorld(creator);
        if (w != null) {
            w.setAutoSave(true);
            w.setGameRule(GameRule.DO_MOB_SPAWNING, false);
            w.setGameRule(GameRule.FALL_DAMAGE, false);
            w.setPVP(false);
            w.setDifficulty(org.bukkit.Difficulty.PEACEFUL);
            w.setSpawnLocation(0, 64, 0);
        }
        return w;
    }

    public boolean isAuthorWorld(World w) {
        if (w == null) return false;
        return w.getName().startsWith("dungeon_") && w.getName().endsWith("_author");
    }

    static class VoidChunkGenerator extends ChunkGenerator {
        @Override
        public void generateNoise(Region region, Random random, int x, int z) {
            // nothing
        }
    }
}
