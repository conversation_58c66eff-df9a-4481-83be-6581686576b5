package com.yourname.aetherdungeons.dungeon.storage;

import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.model.Rules;
import com.yourname.aetherdungeons.dungeon.model.WorldProfile;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class DungeonStorage {

    private final Plugin plugin;
    private final Map<String, Dungeon> dungeons = new HashMap<>();

    public DungeonStorage(Plugin plugin) {
        this.plugin = plugin;
    }

    public Collection<Dungeon> getAll() { return dungeons.values(); }
    public Set<String> getAllIds() { return dungeons.keySet(); }

    public Dungeon getById(String id) {
        return dungeons.get(id.toLowerCase());
    }

    public Dungeon create(String name, WorldProfile profile) {
        String id = name.toLowerCase(Locale.ROOT).replaceAll("[^a-z0-9_\\-]", "_");
        if (id.isBlank()) throw new IllegalArgumentException("Invalid name");
        if (dungeons.containsKey(id)) throw new IllegalArgumentException("Dungeon id already exists");
        Dungeon d = new Dungeon(id, name);
        d.setWorldProfile(profile);
        dungeons.put(id, d);
        save(d);
        return d;
    }

    public void delete(String id) {
        Dungeon d = dungeons.remove(id);
        if (d != null) {
            File f = new File(plugin.getDataFolder(), "dungeons/" + id + ".yml");
            if (f.exists()) f.delete();
        }
    }

    public void loadAll() {
        dungeons.clear();
        File dir = new File(plugin.getDataFolder(), "dungeons");
        File[] files = dir.listFiles((d, name) -> name.endsWith(".yml"));
        if (files == null) return;
        for (File file : files) {
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(file);
            String id = file.getName().substring(0, file.getName().length() - 4);
            String name = cfg.getString("name", id);
            Dungeon d = new Dungeon(id, name);
            d.setWorldProfile(WorldProfile.fromString(cfg.getString("worldProfile", "VOID")));
            // markers
            if (cfg.isConfigurationSection("startBlock")) {
                var s = cfg.getConfigurationSection("startBlock");
                Location loc = new Location(Bukkit.getWorld(d.authorWorldName()),
                        s.getInt("x"), s.getInt("y"), s.getInt("z"));
                d.getMarkers().setStart(loc, Material.matchMaterial(s.getString("type", "GOLD_BLOCK")));
            }
            if (cfg.isConfigurationSection("endBlock")) {
                var s = cfg.getConfigurationSection("endBlock");
                Location loc = new Location(Bukkit.getWorld(d.authorWorldName()),
                        s.getInt("x"), s.getInt("y"), s.getInt("z"));
                d.getMarkers().setEnd(loc, Material.matchMaterial(s.getString("type", "EMERALD_BLOCK")));
            }
            // rules
            Rules r = d.getRules();
            r.setMaxPlayers(cfg.getInt("rules.maxPlayers", 5));
            r.setTimeLimitSeconds(cfg.getInt("rules.timeLimitSeconds", 1800));
            r.setDeathsSpectate("spectate".equalsIgnoreCase(cfg.getString("rules.deaths.mode", "spectate")));
            r.setLivesPerPlayer(cfg.getInt("rules.deaths.livesPerPlayer", 1));
            r.setScalingEnabled(cfg.getBoolean("rules.scaling.enabled", true));
            r.setHpPerPlayerPct(cfg.getInt("rules.scaling.hpPerPlayerPct", 15));
            r.setDmgPerPlayerPct(cfg.getInt("rules.scaling.dmgPerPlayerPct", 10));

            d.setStartCommand(cfg.getString("startCommand", ""));
            d.setExitCommand(cfg.getString("exitCommand", ""));

            // assignments - simplified parallel lists
            var chests = cfg.getMapList("assignments.chests");
            for (var entry : chests) {
                String loot = String.valueOf(entry.get("lootTable"));
                if (entry.get("at") instanceof List<?> list && !list.isEmpty()) {
                    for (Object o : list) {
                        if (o instanceof Map<?,?> m) {
                            int x = Integer.parseInt(String.valueOf(m.get("x")));
                            int y = Integer.parseInt(String.valueOf(m.get("y")));
                            int z = Integer.parseInt(String.valueOf(m.get("z")));
                            d.addChest(new Location(Bukkit.getWorld(d.authorWorldName()), x, y, z), loot);
                        }
                    }
                }
            }
            var mobs = cfg.getMapList("assignments.mobs");
            for (var entry : mobs) {
                String tmpl = String.valueOf(entry.get("template"));
                if (entry.get("at") instanceof List<?> list && !list.isEmpty()) {
                    for (Object o : list) {
                        if (o instanceof Map<?,?> m) {
                            int x = Integer.parseInt(String.valueOf(m.get("x")));
                            int y = Integer.parseInt(String.valueOf(m.get("y")));
                            int z = Integer.parseInt(String.valueOf(m.get("z")));
                            d.addMob(new Location(Bukkit.getWorld(d.authorWorldName()), x, y, z), tmpl);
                        }
                    }
                }
            }
            var bosses = cfg.getMapList("assignments.bosses");
            for (var entry : bosses) {
                String tmpl = String.valueOf(entry.get("template"));
                if (entry.get("at") instanceof Map<?,?> m) {
                    int x = Integer.parseInt(String.valueOf(m.get("x")));
                    int y = Integer.parseInt(String.valueOf(m.get("y")));
                    int z = Integer.parseInt(String.valueOf(m.get("z")));
                    d.addBoss(new Location(Bukkit.getWorld(d.authorWorldName()), x, y, z), tmpl);
                }
            }
            dungeons.put(id, d);
        }
    }

    public void save(Dungeon d) {
        File f = new File(plugin.getDataFolder(), "dungeons/" + d.getId() + ".yml");
        FileConfiguration cfg = new YamlConfiguration();
        cfg.set("id", d.getId());
        cfg.set("name", d.getName());
        cfg.set("worldProfile", d.getWorldProfile().name());
        if (d.getMarkers().getStart() != null) {
            var s = new LinkedHashMap<String,Object>();
            s.put("x", d.getMarkers().getStart().getBlockX());
            s.put("y", d.getMarkers().getStart().getBlockY());
            s.put("z", d.getMarkers().getStart().getBlockZ());
            s.put("type", d.getMarkers().getStartType().name());
            cfg.set("startBlock", s);
        }
        if (d.getMarkers().getEnd() != null) {
            var s = new LinkedHashMap<String,Object>();
            s.put("x", d.getMarkers().getEnd().getBlockX());
            s.put("y", d.getMarkers().getEnd().getBlockY());
            s.put("z", d.getMarkers().getEnd().getBlockZ());
            s.put("type", d.getMarkers().getEndType().name());
            cfg.set("endBlock", s);
        }
        cfg.set("rules.maxPlayers", d.getRules().getMaxPlayers());
        cfg.set("rules.timeLimitSeconds", d.getRules().getTimeLimitSeconds());
        cfg.set("rules.deaths.mode", d.getRules().isDeathsSpectate() ? "spectate" : "eject");
        cfg.set("rules.deaths.livesPerPlayer", d.getRules().getLivesPerPlayer());
        cfg.set("rules.scaling.enabled", d.getRules().isScalingEnabled());
        cfg.set("rules.scaling.hpPerPlayerPct", d.getRules().getHpPerPlayerPct());
        cfg.set("rules.scaling.dmgPerPlayerPct", d.getRules().getDmgPerPlayerPct());

        cfg.set("startCommand", d.getStartCommand());
        cfg.set("exitCommand", d.getExitCommand());

        // assignments
        List<Map<String,Object>> chestList = new ArrayList<>();
        Map<String,Object> chestBlock = new HashMap<>();
        // Group by loot table
        Map<String, List<Location>> grouped = new HashMap<>();
        for (int i = 0; i < d.getChestLocations().size(); i++) {
            String loot = d.getChestLootTables().get(i);
            grouped.computeIfAbsent(loot, k -> new ArrayList<>()).add(d.getChestLocations().get(i));
        }
        for (var e : grouped.entrySet()) {
            Map<String,Object> m = new HashMap<>();
            m.put("lootTable", e.getKey());
            List<Map<String,Integer>> arr = new ArrayList<>();
            for (Location loc : e.getValue()) {
                arr.add(Map.of("x", loc.getBlockX(), "y", loc.getBlockY(), "z", loc.getBlockZ()));
            }
            m.put("at", arr);
            chestList.add(m);
        }
        cfg.set("assignments.chests", chestList);

        List<Map<String,Object>> mobs = new ArrayList<>();
        Map<String, List<Location>> mg = new HashMap<>();
        for (int i = 0; i < d.getMobAnchors().size(); i++) {
            String tmpl = d.getMobTemplates().get(i);
            mg.computeIfAbsent(tmpl, k -> new ArrayList<>()).add(d.getMobAnchors().get(i));
        }
        for (var e : mg.entrySet()) {
            Map<String,Object> m = new HashMap<>();
            m.put("template", e.getKey());
            List<Map<String,Integer>> arr = new ArrayList<>();
            for (Location loc : e.getValue()) {
                arr.add(Map.of("x", loc.getBlockX(), "y", loc.getBlockY(), "z", loc.getBlockZ()));
            }
            m.put("at", arr);
            mobs.add(m);
        }
        cfg.set("assignments.mobs", mobs);

        List<Map<String,Object>> bosses = new ArrayList<>();
        for (int i = 0; i < d.getBossAnchors().size(); i++) {
            Location loc = d.getBossAnchors().get(i);
            String tmpl = d.getBossTemplates().get(i);
            Map<String,Object> m = new HashMap<>();
            m.put("template", tmpl);
            m.put("at", Map.of("x", loc.getBlockX(), "y", loc.getBlockY(), "z", loc.getBlockZ()));
            bosses.add(m);
        }
        cfg.set("assignments.bosses", bosses);

        try {
            cfg.save(f);
        } catch (IOException e) {
            plugin.getLogger().severe("Failed to save dungeon " + d.getId() + ": " + e.getMessage());
        }
    }

    public void saveAll() {
        for (Dungeon d : dungeons.values()) save(d);
    }
}
