package com.yourname.aetherdungeons.spawn.bosses;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.EntityType;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class BossStorage {
    private final Plugin plugin;
    private final Map<String, BossTemplate> bosses = new HashMap<>();

    public BossStorage(Plugin plugin) {
        this.plugin = plugin;
    }

    public void loadAll() {
        bosses.clear();
        File dir = new File(plugin.getDataFolder(), "templates/bosses");
        File[] files = dir.listFiles((d, n) -> n.endsWith(".yml"));
        if (files == null) return;
        for (File f : files) {
            String id = f.getName().substring(0, f.getName().length() - 4);
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(f);
            EntityType type = EntityType.valueOf(cfg.getString("entityType", "WITHER_SKELETON"));
            BossTemplate t = new BossTemplate(id, type);
            t.setName(cfg.getString("name", id));
            t.setMaxHealth(cfg.getDouble("attributes.maxHealth", 300.0));
            t.setDamage(cfg.getDouble("attributes.damage", 10.0));
            bosses.put(id, t);
        }
    }

    public void saveAll() {
        File dir = new File(plugin.getDataFolder(), "templates/bosses");
        for (var e : bosses.entrySet()) {
            File f = new File(dir, e.getKey() + ".yml");
            FileConfiguration cfg = new YamlConfiguration();
            BossTemplate t = e.getValue();
            cfg.set("entityType", t.getType().name());
            cfg.set("name", t.getName());
            cfg.set("attributes.maxHealth", t.getMaxHealth());
            cfg.set("attributes.damage", t.getDamage());
            try {
                cfg.save(f);
            } catch (IOException ex) {
                plugin.getLogger().severe("Failed to save boss template " + e.getKey() + ": " + ex.getMessage());
            }
        }
    }

    public BossTemplate get(String id) { return bosses.get(id); }

    public Set<String> getAllIds() { return bosses.keySet(); }
}
