package com.yourname.aetherdungeons.spawn.mobs;

import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeModifier;

public class MobTemplate {
    private final String id;
    private final org.bukkit.entity.EntityType type;
    private String name;
    private double maxHealth = 20.0;
    private double damage = 2.0;
    private double speed = 0.25;

    public MobTemplate(String id, org.bukkit.entity.EntityType type) {
        this.id = id;
        this.type = type;
    }

    public String getId() { return id; }
    public org.bukkit.entity.EntityType getType() { return type; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public double getMaxHealth() { return maxHealth; }
    public void setMaxHealth(double maxHealth) { this.maxHealth = maxHealth; }
    public double getDamage() { return damage; }
    public void setDamage(double damage) { this.damage = damage; }
    public double getSpeed() { return speed; }
    public void setSpeed(double speed) { this.speed = speed; }
}
