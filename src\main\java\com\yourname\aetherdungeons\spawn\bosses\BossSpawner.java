package com.yourname.aetherdungeons.spawn.bosses;

import com.yourname.aetherdungeons.run.RunState;
import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.Mob;

public class BossSpawner {
    public static void spawnAll(RunState state, BossStorage storage) {
        var dungeon = state.getDungeon();
        var world = state.getWorld();
        for (int i = 0; i < dungeon.getBossAnchors().size(); i++) {
            Location loc = dungeon.getBossAnchors().get(i);
            String id = dungeon.getBossTemplates().get(i);
            BossTemplate t = storage.get(id);
            if (t == null) continue;
            Location in = new Location(world, loc.getX(), loc.getY(), loc.getZ());
            var entity = world.spawnEntity(in, t.getType());
            if (entity instanceof Mob mob) {
                if (t.getName() != null) mob.customName(net.kyori.adventure.text.Component.text(t.getName()));
                var hp = mob.getAttribute(Attribute.GENERIC_MAX_HEALTH);
                if (hp != null) hp.setBaseValue(t.getMaxHealth());
                mob.setHealth(Math.max(1.0, t.getMaxHealth()));
                var dmg = mob.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE);
                if (dmg != null) dmg.setBaseValue(t.getDamage());
            }
        }
    }
}
