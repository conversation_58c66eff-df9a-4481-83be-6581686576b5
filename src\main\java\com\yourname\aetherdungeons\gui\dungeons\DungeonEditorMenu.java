package com.yourname.aetherdungeons.gui.dungeons;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Keys;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.List;

public class DungeonEditorMenu extends Menu {

    private final AetherDungeonsPlugin plugin;
    private final DungeonStorage storage;
    private final Dungeon dungeon;

    public DungeonEditorMenu(AetherDungeonsPlugin plugin, DungeonStorage storage, Dungeon dungeon) {
        super("Editor: " + dungeon.getName(), 45);
        this.plugin = plugin;
        this.storage = storage;
        this.dungeon = dungeon;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.BLUE_STAINED_GLASS_PANE);
        inv.setItem(10, new ItemBuilder(Material.ENDER_PEARL).name("Teleport to Author World").lore(List.of(Component.text("Go to authoring world"))).build());
        inv.setItem(12, new ItemBuilder(Material.NAME_TAG).name("Rename Dungeon").lore(List.of(Component.text("Click to rename"))).build());
        inv.setItem(14, new ItemBuilder(Material.BARRIER).name("Delete Dungeon").lore(List.of(Component.text("Are you sure?"))).build());
        inv.setItem(20, new ItemBuilder(Material.GOLD_BLOCK).name("Start Block").lore(List.of(Component.text("Give Start Placer Wand"))).build());
        inv.setItem(22, new ItemBuilder(Material.EMERALD_BLOCK).name("End Block").lore(List.of(Component.text("Give End Placer Wand"))).build());
        inv.setItem(24, new ItemBuilder(Material.CHEST).name("Chest Placer").lore(List.of(Component.text("Bind chests to a loot table"))).build());
        inv.setItem(30, new ItemBuilder(Material.SKELETON_SPAWN_EGG).name("Mob Placer").lore(List.of(Component.text("Place mob anchors"))).build());
        inv.setItem(32, new ItemBuilder(Material.WITHER_SKELETON_SKULL).name("Boss Placer").lore(List.of(Component.text("Place boss anchors"))).build());
        inv.setItem(40, new ItemBuilder(Material.ARROW).name("Back").build());
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        switch (slot) {
            case 10 -> {
                var w = plugin.getAuthorWorldManager().ensureAuthorWorld(dungeon);
                p.teleport(w.getSpawnLocation());
                p.sendMessage(Component.text("Teleported to " + w.getName()));
            }
            case 12 -> {
                p.closeInventory();
                plugin.getChatPromptManager().prompt(p, "Enter new name:", s -> {
                    dungeon.setName(s);
                    storage.save(dungeon);
                    p.sendMessage(Component.text("Renamed."));
                    new DungeonEditorMenu(plugin, storage, dungeon).open(p);
                });
            }
            case 14 -> {
                p.closeInventory();
                plugin.getChatPromptManager().prompt(p, "Type DELETE to confirm deletion of " + dungeon.getId(), s -> {
                    if ("DELETE".equalsIgnoreCase(s)) {
                        storage.delete(dungeon.getId());
                        p.sendMessage(Component.text("Deleted."));
                    } else {
                        p.sendMessage(Component.text("Cancelled."));
                    }
                    new DungeonsListMenu(plugin, storage).open(p);
                });
            }
            case 20 -> {
                givePlacer(p, "start", Material.GOLD_BLOCK);
                p.sendMessage(Component.text("Given Start Placer. Right-click a block in author world."));
            }
            case 22 -> {
                givePlacer(p, "end", Material.EMERALD_BLOCK);
                p.sendMessage(Component.text("Given End Placer. Right-click a block in author world."));
            }
            case 24 -> {
                p.closeInventory();
                new com.yourname.aetherdungeons.gui.chests.LootSelectorMenu(plugin, dungeon).open(p);
            }
            case 30 -> {
                p.closeInventory();
                new com.yourname.aetherdungeons.gui.mobs.MobTemplateSelectorMenu(plugin, dungeon).open(p);
            }
            case 32 -> {
                p.closeInventory();
                new com.yourname.aetherdungeons.gui.bosses.BossTemplateSelectorMenu(plugin, dungeon).open(p);
            }
            case 40 -> {
                new DungeonsListMenu(plugin, storage).open(p);
            }
        }
    }

    private void givePlacer(Player p, String type, Material display) {
        var item = new ItemBuilder(display).name(type.equals("start") ? "Start Placer" : "End Placer").build();
        ItemMeta meta = item.getItemMeta();
        meta.getPersistentDataContainer().set(Keys.PLACER_TYPE, PersistentDataType.STRING, type);
        meta.getPersistentDataContainer().set(Keys.DUNGEON_ID, PersistentDataType.STRING, dungeon.getId());
        item.setItemMeta(meta);
        p.getInventory().addItem(item);
    }
}
