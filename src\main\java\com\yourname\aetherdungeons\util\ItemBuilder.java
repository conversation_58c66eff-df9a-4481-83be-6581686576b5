package com.yourname.aetherdungeons.util;

import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;

public class ItemBuilder {
    private final ItemStack stack;

    public ItemBuilder(Material material) {
        this.stack = new ItemStack(material);
    }

    public ItemBuilder name(String name) {
        ItemMeta meta = stack.getItemMeta();
        meta.displayName(Component.text(name));
        stack.setItemMeta(meta);
        return this;
    }

    public ItemBuilder lore(List<Component> lines) {
        ItemMeta meta = stack.getItemMeta();
        meta.lore(lines);
        stack.setItemMeta(meta);
        return this;
    }

    public ItemBuilder glow(boolean glow) {
        ItemMeta meta = stack.getItemMeta();
        if (glow) {
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS, ItemFlag.HIDE_ATTRIBUTES);
        }
        stack.setItemMeta(meta);
        return this;
    }

    public ItemStack build() {
        return stack;
    }

    public static ItemStack simple(Material material, String name, List<Component> lore) {
        return new ItemBuilder(material).name(name).lore(lore).build();
    }
}
