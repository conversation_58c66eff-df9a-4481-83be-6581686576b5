package com.yourname.aetherdungeons.run;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class Leaderboards {
    private final Plugin plugin;
    private final Map<String, List<Record>> data = new HashMap<>();

    public Leaderboards(Plugin plugin) {
        this.plugin = plugin;
    }

    public void loadAll() {
        data.clear();
        File dir = new File(plugin.getDataFolder(), "runs");
        File[] files = dir.listFiles((d, n) -> n.endsWith(".yml"));
        if (files == null) return;
        for (File f : files) {
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(f);
            String id = f.getName().substring(0, f.getName().length() - 4);
            List<Record> list = new ArrayList<>();
            var entries = cfg.getMapList("records");
            for (var e : entries) {
                list.add(new Record((String)e.get("player"), (long)(int)e.get("millis")));
            }
            data.put(id, list);
        }
    }

    public void saveAll() {
        File dir = new File(plugin.getDataFolder(), "runs");
        for (var e : data.entrySet()) {
            File f = new File(dir, e.getKey() + ".yml");
            FileConfiguration cfg = new YamlConfiguration();
            List<Map<String,Object>> list = new ArrayList<>();
            for (Record r : e.getValue()) {
                list.add(Map.of("player", r.playerName(), "millis", r.millis()));
            }
            cfg.set("records", list);
            try {
                cfg.save(f);
            } catch (IOException ex) {
                plugin.getLogger().severe("Failed to save leaderboard for " + e.getKey() + ": " + ex.getMessage());
            }
        }
    }

    public void addRecord(String dungeonId, String playerName, long millis) {
        List<Record> list = data.computeIfAbsent(dungeonId, k -> new ArrayList<>());
        list.add(new Record(playerName, millis));
        list.sort(Comparator.comparingLong(Record::millis));
        while (list.size() > 50) list.remove(list.size() - 1);
    }

    public record Record(String playerName, long millis) {}
}
