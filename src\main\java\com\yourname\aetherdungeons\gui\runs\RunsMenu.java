package com.yourname.aetherdungeons.gui.runs;

import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.run.RunManager;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.ArrayList;

public class RunsMenu extends Menu {
    private final RunManager runManager;

    public RunsMenu(RunManager runManager) {
        super("Runs", 54);
        this.runManager = runManager;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.YELLOW_STAINED_GLASS_PANE);
        int i = 10;
        for (Dungeon d : runManager.getDungeonStorage().getAll()) {
            var lore = new ArrayList<Component>();
            lore.add(Component.text("Max Players: " + d.getRules().getMaxPlayers()));
            lore.add(Component.text(d.markersSummary()));
            lore.add(Component.text("Click to start"));
            inv.setItem(i++, new ItemBuilder(Material.CLOCK).name("Start: " + d.getName()).lore(lore).build());
            if (i % 9 == 8) i += 2;
            if (i >= inv.getSize() - 9) break;
        }
        inv.setItem(49, new ItemBuilder(Material.ARROW).name("Back").build());
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        if (slot == 49) {
            p.closeInventory();
            return;
        }
        var item = e.getCurrentItem();
        if (item == null) return;
        String name = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(item.getItemMeta().displayName());
        if (name.startsWith("Start: ")) {
            String dn = name.substring("Start: ".length());
            Dungeon d = runManager.getDungeonStorage().getAll().stream().filter(dd -> dd.getName().equals(dn)).findFirst().orElse(null);
            if (d != null) {
                runManager.tryStartRun(p, d, false);
                p.closeInventory();
            }
        }
    }
}
