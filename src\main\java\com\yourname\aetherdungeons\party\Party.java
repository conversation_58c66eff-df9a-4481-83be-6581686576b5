package com.yourname.aetherdungeons.party;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.*;

public class Party {
    private final UUID owner;
    private final Set<UUID> officers = new HashSet<>();
    private final Set<UUID> members = new LinkedHashSet<>();
    private final Map<UUID, Boolean> ready = new HashMap<>();
    private PartyPrivacy privacy = PartyPrivacy.INVITE_ONLY;

    public Party(UUID owner) {
        this.owner = owner;
        members.add(owner);
        ready.put(owner, false);
    }

    public UUID getOwner() { return owner; }
    public boolean isOwner(UUID id) { return owner.equals(id); }
    public Set<Player> getMembers() {
        Set<Player> out = new LinkedHashSet<>();
        for (UUID id : members) {
            Player p = Bukkit.getPlayer(id);
            if (p != null) out.add(p);
        }
        return out;
    }
    public Set<UUID> getMemberIds() { return new LinkedHashSet<>(members); }
    public boolean addMember(UUID id) {
        boolean added = members.add(id);
        if (added) ready.put(id, false);
        return added;
    }
    public void removeMember(UUID id) {
        members.remove(id);
        ready.remove(id);
        officers.remove(id);
    }
    public boolean isReady(UUID id) {
        return ready.getOrDefault(id, false);
    }
    public void toggleReady(UUID id) {
        ready.put(id, !ready.getOrDefault(id, false));
    }
    public boolean allReady() {
        for (UUID id : members) {
            if (!ready.getOrDefault(id, false)) return false;
        }
        return true;
    }
    public PartyPrivacy getPrivacy() { return privacy; }
    public void setPrivacy(PartyPrivacy privacy) { this.privacy = privacy; }
    public void cyclePrivacy() { this.privacy = this.privacy.next(); }
}
