package com.yourname.aetherdungeons.spawn.chests.listener;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.run.RunManager;
import com.yourname.aetherdungeons.run.RunState;
import com.yourname.aetherdungeons.spawn.chests.LootRoller;
import com.yourname.aetherdungeons.spawn.chests.LootStorage;
import com.yourname.aetherdungeons.util.Keys;
import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.block.Block;
import org.bukkit.block.Chest;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.persistence.PersistentDataType;

public class ChestListener implements Listener {

    private final AetherDungeonsPlugin plugin;
    private final RunManager runManager;
    private final LootStorage lootStorage;
    private final DungeonStorage dungeonStorage;

    public ChestListener(AetherDungeonsPlugin plugin, RunManager runManager, LootStorage lootStorage, DungeonStorage dungeonStorage) {
        this.plugin = plugin;
        this.runManager = runManager;
        this.lootStorage = lootStorage;
        this.dungeonStorage = dungeonStorage;
    }

    @EventHandler
    public void onPlace(BlockPlaceEvent e) {
        var item = e.getItemInHand();
        if (item == null || item.getItemMeta() == null) return;
        var pdc = item.getItemMeta().getPersistentDataContainer();
        String type = pdc.get(Keys.PLACER_TYPE, PersistentDataType.STRING);
        String dungeonId = pdc.get(Keys.DUNGEON_ID, PersistentDataType.STRING);
        if (type == null || dungeonId == null) return;
        Dungeon dungeon = dungeonStorage.getById(dungeonId);
        if (dungeon == null) return;

        Block b = e.getBlockPlaced();
        if (!plugin.getAuthorWorldManager().isAuthorWorld(b.getWorld())) return;

        if ("loot".equals(type)) {
            String lootId = pdc.get(Keys.PLACER_DATA, PersistentDataType.STRING);
            if (b.getType() != Material.CHEST) {
                b.setType(Material.CHEST);
            }
            dungeon.addChest(b.getLocation(), lootId);
            dungeonStorage.save(dungeon);
            e.getPlayer().sendMessage(Component.text("Chest bound to loot '" + lootId + "'."));
        } else if ("start".equals(type)) {
            dungeon.getMarkers().setStart(b.getLocation(), b.getType());
            dungeonStorage.save(dungeon);
            e.getPlayer().sendMessage(Component.text("Start marker set."));
        } else if ("end".equals(type)) {
            dungeon.getMarkers().setEnd(b.getLocation(), b.getType());
            dungeonStorage.save(dungeon);
            e.getPlayer().sendMessage(Component.text("End marker set."));
        } else if ("mob".equals(type)) {
            String templ = pdc.get(Keys.PLACER_DATA, PersistentDataType.STRING);
            dungeon.addMob(b.getLocation(), templ);
            dungeonStorage.save(dungeon);
            e.getPlayer().sendMessage(Component.text("Mob anchor added: " + templ));
        } else if ("boss".equals(type)) {
            String templ = pdc.get(Keys.PLACER_DATA, PersistentDataType.STRING);
            dungeon.addBoss(b.getLocation(), templ);
            dungeonStorage.save(dungeon);
            e.getPlayer().sendMessage(Component.text("Boss anchor added: " + templ));
        }
    }

    @EventHandler
    public void onChestOpen(InventoryOpenEvent e) {
        if (!(e.getInventory().getHolder() instanceof Chest chest)) return;
        var w = chest.getWorld();
        if (!runManager.isRunWorld(w)) return;
        RunState state = runManager.getStateByWorld(w);
        if (state == null) return;

        int x = chest.getX();
        int y = chest.getY();
        int z = chest.getZ();
        if (state.isLooted(x,y,z)) return;

        // Determine loot table by matching configured chest positions
        Dungeon d = state.getDungeon();
        String lootId = null;
        for (int i = 0; i < d.getChestLocations().size(); i++) {
            var loc = d.getChestLocations().get(i);
            if (loc.getBlockX() == x && loc.getBlockY() == y && loc.getBlockZ() == z) {
                lootId = d.getChestLootTables().get(i);
                break;
            }
        }
        if (lootId == null) return;
        var table = lootStorage.get(lootId);
        if (table == null) return;

        chest.getBlockInventory().clear();
        LootRoller.fillInventory(table, chest.getBlockInventory());
        if (table.isOneTimePerRun()) {
            state.markLooted(x,y,z);
        }
    }
}
