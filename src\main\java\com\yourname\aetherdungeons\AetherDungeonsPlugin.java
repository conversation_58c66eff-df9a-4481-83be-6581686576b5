package com.yourname.aetherdungeons;

import com.yourname.aetherdungeons.command.DungeonsCommand;
import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.dungeon.world.AuthorWorldManager;
import com.yourname.aetherdungeons.dungeon.world.InstanceWorldManager;
import com.yourname.aetherdungeons.gui.main.MainMenu;
import com.yourname.aetherdungeons.party.PartyManager;
import com.yourname.aetherdungeons.run.Leaderboards;
import com.yourname.aetherdungeons.run.RunManager;
import com.yourname.aetherdungeons.spawn.bosses.BossStorage;
import com.yourname.aetherdungeons.spawn.chests.LootStorage;
import com.yourname.aetherdungeons.spawn.chests.listener.ChestListener;
import com.yourname.aetherdungeons.spawn.mobs.MobStorage;
import com.yourname.aetherdungeons.util.ChatPromptManager;
import com.yourname.aetherdungeons.util.GuiListener;
import com.yourname.aetherdungeons.util.Keys;
import com.yourname.aetherdungeons.util.ProtectionListener;
import org.bukkit.Bukkit;
import org.bukkit.NamespacedKey;
import org.bukkit.command.PluginCommand;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.util.logging.Logger;

public class AetherDungeonsPlugin extends JavaPlugin {

    private static AetherDungeonsPlugin instance;

    private Logger log;

    private DungeonStorage dungeonStorage;
    private LootStorage lootStorage;
    private MobStorage mobStorage;
    private BossStorage bossStorage;

    private AuthorWorldManager authorWorldManager;
    private InstanceWorldManager instanceWorldManager;
    private PartyManager partyManager;
    private RunManager runManager;
    private Leaderboards leaderboards;
    private ChatPromptManager chatPromptManager;

    public static AetherDungeonsPlugin get() {
        return instance;
    }

    @Override
    public void onEnable() {
        instance = this;
        this.log = getLogger();

        saveDefaultConfig();
        ensureDataFolders();

        // Namespaced keys init
        Keys.init(new NamespacedKey(this, "ad"));

        // Storages
        this.dungeonStorage = new DungeonStorage(this);
        this.lootStorage = new LootStorage(this);
        this.mobStorage = new MobStorage(this);
        this.bossStorage = new BossStorage(this);

        // Managers
        this.authorWorldManager = new AuthorWorldManager(this);
        this.instanceWorldManager = new InstanceWorldManager(this);
        this.partyManager = new PartyManager(this);
        this.leaderboards = new Leaderboards(this);
        this.runManager = new RunManager(this, dungeonStorage, instanceWorldManager, partyManager, leaderboards, mobStorage, bossStorage, lootStorage);
        this.chatPromptManager = new ChatPromptManager(this);

        // Load persisted data
        dungeonStorage.loadAll();
        lootStorage.loadAll();
        mobStorage.loadAll();
        bossStorage.loadAll();
        leaderboards.loadAll();

        // Register command
        DungeonsCommand dungeonsCommand = new DungeonsCommand(this, dungeonStorage, authorWorldManager, runManager, partyManager, chatPromptManager);
        register("dungeons", dungeonsCommand);
        register("dg", dungeonsCommand);
        register("dgstart", dungeonsCommand);
        register("dgexit", dungeonsCommand);

        // Listeners
        Bukkit.getPluginManager().registerEvents(new GuiListener(), this);
        Bukkit.getPluginManager().registerEvents(new ProtectionListener(runManager, authorWorldManager), this);
        Bukkit.getPluginManager().registerEvents(new ChestListener(this, runManager, lootStorage, dungeonStorage), this);

        log.info("AetherDungeons enabled. Use /dungeons to get started.");
    }

    @Override
    public void onDisable() {
        try {
            runManager.shutdownAllRunsSync();
            dungeonStorage.saveAll();
            lootStorage.saveAll();
            mobStorage.saveAll();
            bossStorage.saveAll();
            leaderboards.saveAll();
        } catch (Exception e) {
            getLogger().severe("Error during shutdown: " + e.getMessage());
        }
        instance = null;
    }

    public void openMainMenu(org.bukkit.entity.Player player) {
        new MainMenu(this, dungeonStorage, partyManager, runManager).open(player);
    }

    private void ensureDataFolders() {
        mkdir(new File(getDataFolder(), "dungeons"));
        mkdir(new File(getDataFolder(), "templates/loot"));
        mkdir(new File(getDataFolder(), "templates/mobs"));
        mkdir(new File(getDataFolder(), "templates/bosses"));
        mkdir(new File(getDataFolder(), "runs"));
        mkdir(new File(getDataFolder(), "parties"));
        mkdir(new File(getDataFolder(), "releases")); // optional, fatJar writes to project root/releases
    }

    private void mkdir(File f) {
        if (!f.exists() && !f.mkdirs()) {
            log.warning("Failed to create folder: " + f.getAbsolutePath());
        }
    }

    private void register(String cmd, DungeonsCommand executor) {
        PluginCommand c = getCommand(cmd);
        if (c != null) {
            c.setExecutor(executor);
            c.setTabCompleter(executor);
        } else {
            getLogger().warning("Command not found in plugin.yml: " + cmd);
        }
    }

    public DungeonStorage getDungeonStorage() {
        return dungeonStorage;
    }

    public PartyManager getPartyManager() {
        return partyManager;
    }

    public RunManager getRunManager() {
        return runManager;
    }

    public AuthorWorldManager getAuthorWorldManager() {
        return authorWorldManager;
    }

    public InstanceWorldManager getInstanceWorldManager() {
        return instanceWorldManager;
    }

    public Leaderboards getLeaderboards() {
        return leaderboards;
    }

    public ChatPromptManager getChatPromptManager() {
        return chatPromptManager;
    }
}
