package com.yourname.aetherdungeons.spawn.mobs;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.EntityType;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class MobStorage {
    private final Plugin plugin;
    private final Map<String, MobTemplate> mobs = new HashMap<>();

    public MobStorage(Plugin plugin) {
        this.plugin = plugin;
    }

    public void loadAll() {
        mobs.clear();
        File dir = new File(plugin.getDataFolder(), "templates/mobs");
        File[] files = dir.listFiles((d, n) -> n.endsWith(".yml"));
        if (files == null) return;
        for (File f : files) {
            String id = f.getName().substring(0, f.getName().length() - 4);
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(f);
            EntityType type = EntityType.valueOf(cfg.getString("entityType", "ZOMBIE"));
            MobTemplate t = new MobTemplate(id, type);
            t.setName(cfg.getString("name", id));
            t.setMaxHealth(cfg.getDouble("attributes.maxHealth", 20.0));
            t.setDamage(cfg.getDouble("attributes.damage", 2.0));
            t.setSpeed(cfg.getDouble("attributes.speed", 0.25));
            mobs.put(id, t);
        }
    }

    public void saveAll() {
        File dir = new File(plugin.getDataFolder(), "templates/mobs");
        for (var e : mobs.entrySet()) {
            File f = new File(dir, e.getKey() + ".yml");
            FileConfiguration cfg = new YamlConfiguration();
            MobTemplate t = e.getValue();
            cfg.set("entityType", t.getType().name());
            cfg.set("name", t.getName());
            cfg.set("attributes.maxHealth", t.getMaxHealth());
            cfg.set("attributes.damage", t.getDamage());
            cfg.set("attributes.speed", t.getSpeed());
            try {
                cfg.save(f);
            } catch (IOException ex) {
                plugin.getLogger().severe("Failed to save mob template " + e.getKey() + ": " + ex.getMessage());
            }
        }
    }

    public MobTemplate get(String id) { return mobs.get(id); }

    public Set<String> getAllIds() { return mobs.keySet(); }
}
