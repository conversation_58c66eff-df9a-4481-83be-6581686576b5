package com.yourname.aetherdungeons.dungeon.world;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import org.bukkit.Bukkit;
import org.bukkit.GameRule;
import org.bukkit.World;
import org.bukkit.WorldCreator;

import java.io.IOException;
import java.nio.file.*;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

public class InstanceWorldManager {

    private final AetherDungeonsPlugin plugin;

    public InstanceWorldManager(AetherDungeonsPlugin plugin) {
        this.plugin = plugin;
    }

    public CompletableFuture<World> createInstanceFromAuthor(Dungeon dungeon, long ts) {
        World author = dungeon.getAuthorWorld();
        if (author == null) {
            author = plugin.getAuthorWorldManager().ensureAuthorWorld(dungeon);
        }
        World finalAuthor = author;
        String worldName = dungeon.instanceWorldName(ts);
        Path src = Path.of(Bukkit.getWorldContainer().getAbsolutePath(), finalAuthor.getName());
        Path dst = Path.of(Bukkit.getWorldContainer().getAbsolutePath(), worldName);

        return CompletableFuture.supplyAsync(() -> {
            try {
                copyWorldFolder(src, dst);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return null;
        }).thenApplyAsync(nil -> {
            WorldCreator creator = new WorldCreator(worldName);
            World w = Bukkit.createWorld(creator);
            if (w != null) {
                w.setGameRule(GameRule.DO_MOB_SPAWNING, true);
                w.setPVP(true);
            }
            return w;
        }, Bukkit.getScheduler().getMainThreadExecutor(plugin));
    }

    public void unloadAndDeleteWorld(World w) {
        String name = w.getName();
        Bukkit.unloadWorld(w, true);
        Path dst = Path.of(Bukkit.getWorldContainer().getAbsolutePath(), name);
        try {
            deleteRecursive(dst);
        } catch (IOException e) {
            plugin.getLogger().warning("Failed to delete instance world folder: " + e.getMessage());
        }
    }

    public boolean isInstanceWorld(World w) {
        return w != null && w.getName().contains("_run_");
    }

    private static final Set<String> EXCLUDE = Set.of("session.lock", "uid.dat", "playerdata", "advancements", "stats");

    private void copyWorldFolder(Path source, Path target) throws IOException {
        if (Files.exists(target)) {
            deleteRecursive(target);
        }
        Files.createDirectories(target);
        try (var walk = Files.walk(source)) {
            walk.forEach(path -> {
                try {
                    Path rel = source.relativize(path);
                    if (rel.toString().isEmpty()) return;
                    String name = rel.getFileName().toString();
                    for (String ex : EXCLUDE) {
                        if (rel.startsWith(ex)) return;
                    }
                    Path dest = target.resolve(rel);
                    if (Files.isDirectory(path)) {
                        Files.createDirectories(dest);
                    } else {
                        Files.copy(path, dest, StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.COPY_ATTRIBUTES);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private void deleteRecursive(Path path) throws IOException {
        if (!Files.exists(path)) return;
        try (var walk = Files.walk(path)) {
            walk.sorted((a, b) -> b.getNameCount() - a.getNameCount()).forEach(p -> {
                try {
                    Files.deleteIfExists(p);
                } catch (IOException ignored) { }
            });
        }
    }
}
