package com.yourname.aetherdungeons.gui.parties;

import com.yourname.aetherdungeons.party.PartyManager;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.UUID;

public class InvitePromptMenu extends Menu {
    private final PartyManager partyManager;
    private final UUID ownerId;

    public InvitePromptMenu(PartyManager partyManager, UUID ownerId) {
        super("Party Invite", 27);
        this.partyManager = partyManager;
        this.ownerId = ownerId;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.MAGENTA_STAINED_GLASS_PANE);
        var owner = Bukkit.getOfflinePlayer(ownerId);
        inv.setItem(11, new ItemBuilder(Material.LIME_CONCRETE).name("Accept invite from " + (owner != null ? owner.getName() : "unknown")).build());
        inv.setItem(15, new ItemBuilder(Material.RED_CONCRETE).name("Decline").build());
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        if (slot == 11) {
            boolean ok = partyManager.acceptInvite(p);
            p.closeInventory();
            p.sendMessage(Component.text(ok ? "Joined party." : "Invite invalid."));
        } else if (slot == 15) {
            partyManager.declineInvite(p);
            p.closeInventory();
            p.sendMessage(Component.text("Invite declined."));
        }
    }
}
