package com.yourname.aetherdungeons.command;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.dungeon.world.AuthorWorldManager;
import com.yourname.aetherdungeons.gui.main.MainMenu;
import com.yourname.aetherdungeons.party.PartyManager;
import com.yourname.aetherdungeons.run.RunManager;
import com.yourname.aetherdungeons.util.ChatPromptManager;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.World;
    import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class DungeonsCommand implements CommandExecutor, TabCompleter {

    private final AetherDungeonsPlugin plugin;
    private final DungeonStorage dungeonStorage;
    private final AuthorWorldManager authorWorldManager;
    private final RunManager runManager;
    private final PartyManager partyManager;
    private final ChatPromptManager prompts;

    public DungeonsCommand(AetherDungeonsPlugin plugin,
                           DungeonStorage dungeonStorage,
                           AuthorWorldManager authorWorldManager,
                           RunManager runManager,
                           PartyManager partyManager,
                           ChatPromptManager prompts) {
        this.plugin = plugin;
        this.dungeonStorage = dungeonStorage;
        this.authorWorldManager = authorWorldManager;
        this.runManager = runManager;
        this.partyManager = partyManager;
        this.prompts = prompts;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player p)) {
            sender.sendMessage("Players only.");
            return true;
        }
        String cmd = command.getName().toLowerCase();
        switch (cmd) {
            case "dungeons", "dg" -> {
                new MainMenu(plugin, dungeonStorage, partyManager, runManager).open(p);
                return true;
            }
            case "dgstart" -> {
                if (args.length == 1) {
                    String id = args[0];
                    Dungeon d = dungeonStorage.getById(id);
                    if (d == null) {
                        p.sendMessage(Component.text("Dungeon not found: " + id));
                        return true;
                    }
                    runManager.tryStartRun(p, d, true);
                } else {
                    p.sendMessage(Component.text("Usage: /dgstart <dungeonId>"));
                }
                return true;
            }
            case "dgexit" -> {
                runManager.exitPlayer(p);
                return true;
            }
            default -> {
            }
        }

        if (args.length == 0) {
            new MainMenu(plugin, dungeonStorage, partyManager, runManager).open(p);
            return true;
        }

        String sub = args[0].toLowerCase();
        switch (sub) {
            case "tp" -> {
                if (args.length < 2) {
                    p.sendMessage(Component.text("Usage: /dg tp <dungeonId>"));
                    return true;
                }
                Dungeon d = dungeonStorage.getById(args[1]);
                if (d == null) {
                    p.sendMessage(Component.text("Dungeon not found."));
                    return true;
                }
                World w = authorWorldManager.ensureAuthorWorld(d);
                p.teleport(w.getSpawnLocation());
                p.sendMessage(Component.text("Teleported to author world: " + w.getName()));
                return true;
            }
            case "setstart" -> {
                if (args.length < 2) {
                    p.sendMessage(Component.text("Usage: /dg setstart <dungeonId> while looking at a block"));
                    return true;
                }
                Dungeon d = dungeonStorage.getById(args[1]);
                if (d == null) {
                    p.sendMessage(Component.text("Dungeon not found."));
                    return true;
                }
                var target = p.getTargetBlockExact(6);
                if (target == null) {
                    p.sendMessage(Component.text("Look at a block within 6 blocks."));
                    return true;
                }
                d.getMarkers().setStart(target.getLocation(), target.getType());
                dungeonStorage.save(d);
                p.sendMessage(Component.text("Start set at " + target.getLocation().toVector().toString() + " type=" + target.getType()));
                return true;
            }
            case "setend" -> {
                if (args.length < 2) {
                    p.sendMessage(Component.text("Usage: /dg setend <dungeonId> while looking at a block"));
                    return true;
                }
                Dungeon d = dungeonStorage.getById(args[1]);
                if (d == null) {
                    p.sendMessage(Component.text("Dungeon not found."));
                    return true;
                }
                var target = p.getTargetBlockExact(6);
                if (target == null) {
                    p.sendMessage(Component.text("Look at a block within 6 blocks."));
                    return true;
                }
                d.getMarkers().setEnd(target.getLocation(), target.getType());
                dungeonStorage.save(d);
                p.sendMessage(Component.text("End set at " + target.getLocation().toVector().toString() + " type=" + target.getType()));
                return true;
            }
            case "start" -> {
                if (args.length < 2) {
                    p.sendMessage(Component.text("Usage: /dg start <dungeonId>"));
                    return true;
                }
                Dungeon d = dungeonStorage.getById(args[1]);
                if (d == null) {
                    p.sendMessage(Component.text("Dungeon not found."));
                    return true;
                }
                runManager.tryStartRun(p, d, p.hasPermission("aetherdungeons.admin.*"));
                return true;
            }
            default -> {
                new MainMenu(plugin, dungeonStorage, partyManager, runManager).open(p);
                return true;
            }
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            List<String> subs = List.of("tp", "setstart", "setend", "start");
            return subs.stream().filter(s -> s.startsWith(args[0].toLowerCase())).collect(Collectors.toList());
        }
        if (args.length == 2) {
            List<String> ids = new ArrayList<>(dungeonStorage.getAllIds());
            return ids.stream().filter(s -> s.startsWith(args[1].toLowerCase())).collect(Collectors.toList());
        }
        return List.of();
    }
}
