package com.yourname.aetherdungeons.gui.parties;

import com.yourname.aetherdungeons.party.Party;
import com.yourname.aetherdungeons.party.PartyManager;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.List;

public class PartiesMenu extends Menu {
    private final PartyManager partyManager;

    public PartiesMenu(PartyManager partyManager) {
        super("Parties", 54);
        this.partyManager = partyManager;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.GREEN_STAINED_GLASS_PANE);
        inv.setItem(10, new ItemBuilder(Material.LIME_CONCRETE).name("Create/Leave").lore(List.of(Component.text("Create a party if none; else leave"))).build());
        inv.setItem(12, new ItemBuilder(Material.PAPER).name("Invites").lore(List.of(Component.text("Invite online players"))).build());
        inv.setItem(14, new ItemBuilder(Material.REDSTONE_TORCH).name("Ready Toggle").lore(List.of(Component.text("Toggle ready state"))).build());
        inv.setItem(16, new ItemBuilder(Material.IRON_DOOR).name("Privacy").lore(List.of(Component.text("Cycle privacy"))).build());
        inv.setItem(49, new ItemBuilder(Material.ARROW).name("Back").build());

        Party party = partyManager.getPartyOf(getViewer());
        if (party != null) {
            int i = 28;
            for (var mem : party.getMembers()) {
                inv.setItem(i++, new ItemBuilder(Material.PLAYER_HEAD).name(mem.getName()).lore(List.of(Component.text(party.isReady(mem.getUniqueId()) ? "Ready" : "Not Ready"))).build());
            }
        }
    }

    private Player getViewer() {
        // Called only while opened
        return (Player) Bukkit.getOnlinePlayers().stream().filter(p -> p.getOpenInventory().getTopInventory().getHolder() == this).findFirst().orElse(null);
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        Party party = partyManager.getPartyOf(p);
        if (slot == 10) {
            if (party == null) {
                partyManager.createParty(p);
                p.sendMessage(Component.text("Party created."));
            } else {
                partyManager.leaveParty(p);
                p.sendMessage(Component.text("Left party."));
            }
            build();
            p.updateInventory();
        } else if (slot == 12) {
            if (party == null || !party.isOwner(p.getUniqueId())) {
                p.sendMessage(Component.text("You must be the party owner."));
                return;
            }
            new InviteMenu(partyManager, p).open(p);
        } else if (slot == 14) {
            partyManager.toggleReady(p);
            build();
            p.updateInventory();
        } else if (slot == 16) {
            if (party != null && party.isOwner(p.getUniqueId())) {
                partyManager.cyclePrivacy(party);
                p.sendMessage(Component.text("Privacy: " + party.getPrivacy()));
            }
        } else if (slot == 49) {
            p.closeInventory();
        }
    }

    static class InviteMenu extends Menu {
        private final PartyManager partyManager;
        private final Player owner;

        public InviteMenu(PartyManager partyManager, Player owner) {
            super("Invite Players", 54);
            this.partyManager = partyManager;
            this.owner = owner;
        }

        @Override
        protected void build() {
            inv.clear();
            fillBorders(Material.WHITE_STAINED_GLASS_PANE);
            int i = 10;
            for (Player online : owner.getServer().getOnlinePlayers()) {
                if (online.equals(owner)) continue;
                inv.setItem(i++, new ItemBuilder(Material.PLAYER_HEAD).name(online.getName()).lore(List.of(Component.text("Click to invite"))).build());
                if (i % 9 == 8) i += 2;
                if (i >= inv.getSize() - 9) break;
            }
            inv.setItem(49, new ItemBuilder(Material.ARROW).name("Back").build());
        }

        @Override
        public void onClick(InventoryClickEvent e) {
            Player p = (Player) e.getWhoClicked();
            int slot = e.getRawSlot();
            if (slot == 49) {
                new PartiesMenu(partyManager).open(p);
                return;
            }
            var item = e.getCurrentItem();
            if (item == null) return;
            String name = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(item.getItemMeta().displayName());
            Player target = Bukkit.getPlayerExact(name);
            if (target != null) {
                partyManager.sendInvite(owner, target);
                p.sendMessage(Component.text("Invited " + name));
            }
        }
    }
}
