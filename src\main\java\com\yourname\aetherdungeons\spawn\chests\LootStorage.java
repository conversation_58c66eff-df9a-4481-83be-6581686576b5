package com.yourname.aetherdungeons.spawn.chests;

import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class LootStorage {
    private final Plugin plugin;
    private final Map<String, LootTable> tables = new HashMap<>();

    public LootStorage(Plugin plugin) {
        this.plugin = plugin;
    }

    public void loadAll() {
        tables.clear();
        File dir = new File(plugin.getDataFolder(), "templates/loot");
        File[] files = dir.listFiles((d, n) -> n.endsWith(".yml"));
        if (files == null) return;
        for (File f : files) {
            String id = f.getName().substring(0, f.getName().length() - 4);
            FileConfiguration cfg = YamlConfiguration.loadConfiguration(f);
            LootTable table = new LootTable(id);
            if (cfg.isConfigurationSection("rolls")) {
                table.setRollsMin(cfg.getInt("rolls.min", 1));
                table.setRollsMax(cfg.getInt("rolls.max", 2));
            }
            table.setOneTimePerRun(cfg.getBoolean("oneTimePerRun", true));
            var entries = cfg.getMapList("entries");
            for (var e : entries) {
                Material mat = Material.matchMaterial(String.valueOf(e.get("item")));
                int min = Integer.parseInt(String.valueOf(e.get("min")));
                int max = Integer.parseInt(String.valueOf(e.get("max")));
                int weight = Integer.parseInt(String.valueOf(e.get("weight")));
                if (mat != null) table.addEntry(new LootEntry(mat, min, max, weight));
            }
            tables.put(id, table);
        }
    }

    public void saveAll() {
        File dir = new File(plugin.getDataFolder(), "templates/loot");
        for (var e : tables.entrySet()) {
            File f = new File(dir, e.getKey() + ".yml");
            FileConfiguration cfg = new YamlConfiguration();
            LootTable t = e.getValue();
            cfg.set("rolls.min", t.getRollsMin());
            cfg.set("rolls.max", t.getRollsMax());
            cfg.set("oneTimePerRun", t.isOneTimePerRun());
            List<Map<String,Object>> list = new ArrayList<>();
            for (LootEntry le : t.getEntries()) {
                list.add(Map.of("item", le.item.name(), "min", le.min, "max", le.max, "weight", le.weight));
            }
            cfg.set("entries", list);
            try {
                cfg.save(f);
            } catch (IOException ex) {
                plugin.getLogger().severe("Failed to save loot table " + e.getKey() + ": " + ex.getMessage());
            }
        }
    }

    public LootTable get(String id) { return tables.get(id); }

    public Set<String> getAllIds() { return tables.keySet(); }
}
