package com.yourname.aetherdungeons.util;

import io.papermc.paper.event.player.AsyncChatEvent;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

public class ChatPromptManager implements Listener {

    private final Map<UUID, Consumer<String>> prompts = new ConcurrentHashMap<>();

    public ChatPromptManager(org.bukkit.plugin.Plugin plugin) {
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    public void prompt(Player player, String message, Consumer<String> consumer) {
        player.sendMessage(Component.text(message));
        prompts.put(player.getUniqueId(), consumer);
    }

    @EventHandler
    public void onChat(AsyncChatEvent event) {
        UUID id = event.getPlayer().getUniqueId();
        Consumer<String> consumer = prompts.remove(id);
        if (consumer != null) {
            event.setCancelled(true);
            String plain = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(event.message());
            Bukkit.getScheduler().runTask(Bukkit.getPluginManager().getPlugin("AetherDungeons"), () -> consumer.accept(plain));
        }
    }
}
