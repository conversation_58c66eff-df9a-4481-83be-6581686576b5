# AetherDungeons

AetherDungeons is a GUI-first instanced dungeon system for Paper 1.21.x built with Java 21 and the Paper API only. No external runtime dependencies.

Features (MVP)
- GUI-first management via /dungeons (alias /dg).
- Create dungeons, auto-create an authoring world (void or superflat).
- Set Start/End blocks via GUI wands or commands.
- Chest Loot Tables and Chest Placer to pre-place author chests.
- Minimal Mob/Boss templates and placers; spawn at anchors on run start.
- Party system with GUI: create, invite, ready toggles.
- Instanced runs: clones the author world into a temporary run world.
- Leaderboards: best run times saved to YAML.

Planned/Expandable
- Advanced GUI tabs for templates, phases, abilities, scaling rules, rewards.
- Spectator mode, checkpoints, daily/weekly first-clear bonuses, cosmetics.

## Requirements
- Paper 1.21.x
- Java 21

## Installation
1. Build the plugin:
   - `./gradlew fatJar`
   - Jar will be output to `releases/AetherDungeons-1.0.0.jar` (from project root).
2. Copy the jar into your server's `plugins/` directory.
3. Start the server. Ensure no errors in the console.
4. Use `/dungeons` to open the main GUI.

## Commands
- `/dungeons` or `/dg` — open the main GUI.
- `/dg tp <dungeon>` — teleport to a dungeon's author world.
- `/dg setstart <dungeon>` — set the looking-at block as the start marker.
- `/dg setend <dungeon>` — set the looking-at block as the end marker.
- `/dg start <dungeon>` — attempt to start a run for your party.
- `/dgstart <dungeon>` — force-start a run (if you have permission).
- `/dg exit` — exit the current run.

## Permissions
- `aetherdungeons.play` — default true
- `aetherdungeons.party` — default true
- `aetherdungeons.editor` — default op
- `aetherdungeons.admin.*` — default op

## Getting Started (GUI-First)
1. `/dungeons` -> Dungeons tab -> Create Dungeon.
2. Choose a world profile (Void or Superflat).
3. Teleport to the author world; build your dungeon there.
4. In Editor: Get Start/End Placer wands and right-click blocks to set markers.
5. Create loot tables by placing yml files in `plugins/AetherDungeons/templates/loot` or using defaults, then use Chest Placer to bind chests.
6. Create mob/boss templates by placing yml files in `plugins/AetherDungeons/templates/mobs` and `templates/bosses`, then use placers to set anchors.
7. Create a party in the Parties tab, invite members, and toggle Ready.
8. Start the run from the Runs tab or `/dg start <dungeon>`. A temporary instance world will be created and you’ll be teleported to Start.
9. Open chests to roll loot, fight mobs/bosses, and use the End block to complete and record a time.

## Persistence Layout
- `config.yml` — global settings.
- `dungeons/<id>.yml` — dungeon configuration (markers, profile, rules, assignments).
- `templates/loot/*.yml` — loot tables.
- `templates/mobs/*.yml` — mob templates.
- `templates/bosses/*.yml` — boss templates.
- `runs/<dungeon>.yml` — leaderboard records.

## Building
- Requires the Gradle Wrapper included.
- `./gradlew fatJar` — creates `releases/AetherDungeons-1.0.0.jar`.

## Notes
- World cloning is implemented by cloning the author world folder to a new run world folder asynchronously, then loading it. This is widely used in practice; do not edit run worlds directly except via plugin logic.
- The MVP implements foundational systems; you can expand GUIs and template details as needed.
- No external dependencies are used at runtime.

## License
MIT — see LICENSE
