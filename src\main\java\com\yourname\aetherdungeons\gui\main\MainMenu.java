package com.yourname.aetherdungeons.gui.main;

import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.gui.dungeons.DungeonsListMenu;
import com.yourname.aetherdungeons.party.PartyManager;
import com.yourname.aetherdungeons.run.RunManager;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

public class MainMenu extends Menu {

    private final com.yourname.aetherdungeons.AetherDungeonsPlugin plugin;
    private final DungeonStorage dungeonStorage;
    private final PartyManager partyManager;
    private final RunManager runManager;

    public MainMenu(com.yourname.aetherdungeons.AetherDungeonsPlugin plugin, DungeonStorage dungeonStorage, PartyManager partyManager, RunManager runManager) {
        super("AetherDungeons", 27);
        this.plugin = plugin;
        this.dungeonStorage = dungeonStorage;
        this.partyManager = partyManager;
        this.runManager = runManager;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.GRAY_STAINED_GLASS_PANE);
        inv.setItem(10, new ItemBuilder(Material.ENDER_EYE).name("Dungeons").lore(java.util.List.of(Component.text("Create/Edit dungeons"), Component.text("Open the dungeon manager"))).build());
        inv.setItem(12, new ItemBuilder(Material.PLAYER_HEAD).name("Parties").lore(java.util.List.of(Component.text("Create or manage your party"))).build());
        inv.setItem(14, new ItemBuilder(Material.CLOCK).name("Runs").lore(java.util.List.of(Component.text("Start or spectate runs"))).build());
        inv.setItem(16, new ItemBuilder(Material.COMPARATOR).name("Settings").lore(java.util.List.of(Component.text("Plugin settings"))).build());
        inv.setItem(22, new ItemBuilder(Material.BARRIER).name("Close").build());
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        if (slot == 10) {
            new DungeonsListMenu(plugin, dungeonStorage).open(p);
        } else if (slot == 12) {
            new com.yourname.aetherdungeons.gui.parties.PartiesMenu(partyManager).open(p);
        } else if (slot == 14) {
            new com.yourname.aetherdungeons.gui.runs.RunsMenu(runManager).open(p);
        } else if (slot == 16) {
            new com.yourname.aetherdungeons.gui.settings.SettingsMenu().open(p);
        } else if (slot == 22) {
            p.closeInventory();
        }
    }
}
