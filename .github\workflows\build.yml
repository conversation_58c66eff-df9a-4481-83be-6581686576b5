name: Build

on:
  push:
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: '21'
          cache: gradle

      - name: Make gradlew executable
        run: chmod +x gradlew

      - name: Build with Gradle
        run: ./gradlew --no-daemon clean fatJar

      - name: Upload Jar artifact
        uses: actions/upload-artifact@v4
        with:
          name: AetherDungeons-jar
          path: releases/*.jar
          if-no-files-found: error
