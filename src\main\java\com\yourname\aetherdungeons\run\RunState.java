package com.yourname.aetherdungeons.run;

import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import org.bukkit.World;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class RunState {
    private final Dungeon dungeon;
    private final World world;
    private final long startMillis;
    private final Set<String> lootedChestKeys = new HashSet<>();
    private final Set<UUID> players = new HashSet<>();

    public RunState(Dungeon dungeon, World world, long startMillis) {
        this.dungeon = dungeon;
        this.world = world;
        this.startMillis = startMillis;
    }

    public Dungeon getDungeon() { return dungeon; }
    public World getWorld() { return world; }
    public long getStartMillis() { return startMillis; }

    public Set<String> getLootedChestKeys() { return lootedChestKeys; }
    public void markLooted(int x, int y, int z) { lootedChestKeys.add(x+":"+y+":"+z); }
    public boolean isLooted(int x, int y, int z) { return lootedChestKeys.contains(x+":"+y+":"+z); }

    public Set<UUID> getPlayers() { return players; }
}
