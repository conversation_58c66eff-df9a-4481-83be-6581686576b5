package com.yourname.aetherdungeons.util;

import org.bukkit.NamespacedKey;

public final class Keys {
    private Keys() {}
    private static NamespacedKey base;

    public static void init(NamespacedKey baseKey) {
        base = baseKey;
    }

    public static NamespacedKey sub(String name) {
        return new NamespacedKey(base.getNamespace(), base.getKey() + "_" + name);
    }

    public static NamespacedKey MENU_ID = new NamespacedKey("aetherdungeons", "menu_id");
    public static NamespacedKey PLACER_TYPE = new NamespacedKey("aetherdungeons", "placer_type"); // start,end,loot,mob,boss
    public static NamespacedKey PLACER_DATA = new NamespacedKey("aetherdungeons", "placer_data"); // e.g., lootTableId or template id
    public static NamespacedKey DUNGEON_ID = new NamespacedKey("aetherdungeons", "dungeon_id");
}
