package com.yourname.aetherdungeons.party;

import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class PartyManager {
    private final Plugin plugin;
    private final Map<UUID, Party> partiesByMember = new ConcurrentHashMap<>();
    private final Map<UUID, UUID> pendingInvites = new ConcurrentHashMap<>(); // invited -> owner

    public PartyManager(Plugin plugin) {
        this.plugin = plugin;
    }

    public Party getPartyOf(Player p) {
        return partiesByMember.get(p.getUniqueId());
    }

    public Party createParty(Player owner) {
        Party existing = getPartyOf(owner);
        if (existing != null) return existing;
        Party party = new Party(owner.getUniqueId());
        for (UUID id : party.getMemberIds()) {
            partiesByMember.put(id, party);
        }
        return party;
    }

    public void leaveParty(Player p) {
        Party party = getPartyOf(p);
        if (party == null) return;
        party.removeMember(p.getUniqueId());
        partiesByMember.remove(p.getUniqueId());
        if (party.getMemberIds().isEmpty() || party.getOwner().equals(p.getUniqueId())) {
            // disband
            for (UUID id : new ArrayList<>(party.getMemberIds())) {
                partiesByMember.remove(id);
            }
        }
    }

    public void sendInvite(Player owner, Player target) {
        Party party = getPartyOf(owner);
        if (party == null) {
            owner.sendMessage(Component.text("Create a party first."));
            return;
        }
        pendingInvites.put(target.getUniqueId(), owner.getUniqueId());
        target.sendMessage(Component.text(owner.getName() + " invited you to a party."));
        Bukkit.getScheduler().runTask(plugin, () -> {
            new com.yourname.aetherdungeons.gui.parties.InvitePromptMenu(this, owner.getUniqueId()).open(target);
        });
    }

    public boolean acceptInvite(Player target) {
        UUID ownerId = pendingInvites.remove(target.getUniqueId());
        if (ownerId == null) return false;
        Player owner = Bukkit.getPlayer(ownerId);
        if (owner == null) return false;
        Party party = getPartyOf(owner);
        if (party == null) return false;
        party.addMember(target.getUniqueId());
        partiesByMember.put(target.getUniqueId(), party);
        return true;
    }

    public void declineInvite(Player target) {
        pendingInvites.remove(target.getUniqueId());
    }

    public void toggleReady(Player p) {
        Party party = getPartyOf(p);
        if (party != null) party.toggleReady(p.getUniqueId());
    }

    public void cyclePrivacy(Party party) {
        party.cyclePrivacy();
    }
}
