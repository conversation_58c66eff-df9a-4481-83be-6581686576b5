package com.yourname.aetherdungeons.gui.dungeons;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.dungeon.model.WorldProfile;
import com.yourname.aetherdungeons.dungeon.storage.DungeonStorage;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.ArrayList;
import java.util.List;

public class DungeonsListMenu extends Menu {

    private final AetherDungeonsPlugin plugin;
    private final DungeonStorage storage;

    public DungeonsListMenu(AetherDungeonsPlugin plugin, DungeonStorage storage) {
        super("Dungeons", 54);
        this.plugin = plugin;
        this.storage = storage;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.CYAN_STAINED_GLASS_PANE);
        int i = 10;
        for (Dungeon d : storage.getAll()) {
            List<Component> lore = new ArrayList<>();
            lore.add(Component.text("ID: " + d.getId()));
            lore.add(Component.text("Profile: " + d.getWorldProfile()));
            lore.add(Component.text(d.markersSummary()));
            lore.add(Component.text("Click to open Editor"));
            inv.setItem(i, new ItemBuilder(Material.ENDER_PEARL).name(d.getName()).lore(lore).build());
            i++;
            if (i % 9 == 8) i += 2; // simple next row
            if (i >= inv.getSize() - 9) break;
        }
        inv.setItem(49, new ItemBuilder(Material.LIME_CONCRETE).name("Create Dungeon").lore(List.of(Component.text("Click to create a new dungeon"))).build());
        inv.setItem(53, new ItemBuilder(Material.ARROW).name("Back").build());
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        if (slot == 49) {
            p.closeInventory();
            plugin.getChatPromptManager().prompt(p, "Enter dungeon name in chat:", name -> {
                if (name == null || name.isBlank()) {
                    p.sendMessage(Component.text("Cancelled."));
                    return;
                }
                // profile selection simple two-button menu
                new WorldProfileMenu(plugin, storage, name).open(p);
            });
            return;
        }
        if (slot == 53) {
            new com.yourname.aetherdungeons.gui.main.MainMenu(plugin, storage, plugin.getPartyManager(), plugin.getRunManager()).open(p);
            return;
        }
        var item = e.getCurrentItem();
        if (item != null && item.getType() == Material.ENDER_PEARL) {
            String name = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(item.getItemMeta().displayName());
            Dungeon d = storage.getAll().stream().filter(dd -> dd.getName().equals(name)).findFirst().orElse(null);
            if (d != null) {
                new DungeonEditorMenu(plugin, storage, d).open(p);
            }
        }
    }

    static class WorldProfileMenu extends Menu {
        private final AetherDungeonsPlugin plugin;
        private final DungeonStorage storage;
        private final String name;

        public WorldProfileMenu(AetherDungeonsPlugin plugin, DungeonStorage storage, String name) {
            super("Select World Profile", 27);
            this.plugin = plugin;
            this.storage = storage;
            this.name = name;
        }

        @Override
        protected void build() {
            inv.clear();
            fillBorders(Material.BLACK_STAINED_GLASS_PANE);
            inv.setItem(11, new ItemBuilder(Material.BARRIER).name("VOID").lore(List.of(Component.text("Empty void world"))).build());
            inv.setItem(15, new ItemBuilder(Material.GRASS_BLOCK).name("SUPERFLAT").lore(List.of(Component.text("Minimal superflat"))).build());
        }

        @Override
        public void onClick(InventoryClickEvent e) {
            Player p = (Player) e.getWhoClicked();
            var item = e.getCurrentItem();
            if (item == null) return;
            WorldProfile profile = item.getType() == Material.GRASS_BLOCK ? WorldProfile.SUPERFLAT : WorldProfile.VOID;
            try {
                Dungeon d = storage.create(name, profile);
                plugin.getAuthorWorldManager().ensureAuthorWorld(d);
                p.sendMessage(Component.text("Dungeon created: " + d.getId() + " (" + profile + ")"));
                new DungeonEditorMenu(plugin, storage, d).open(p);
            } catch (Exception ex) {
                p.sendMessage(Component.text("Failed: " + ex.getMessage()));
                new DungeonsListMenu(plugin, storage).open(p);
            }
        }
    }
}
