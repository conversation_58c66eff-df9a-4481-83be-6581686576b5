import jdk.tools.jlink.resources.plugins

plugins {
    java
}

group = "com.yourname"
version = "1.0.0"

java {
    toolchain.languageVersion.set(JavaLanguageVersion.of(21))
    withSourcesJar()
}

repositories {
    mavenCentral()
    maven("https://repo.papermc.io/repository/maven-public/")
}

dependencies {
    compileOnly("io.papermc.paper:paper-api:1.21.1-R0.1-SNAPSHOT")
    testImplementation(platform("org.junit:junit-bom:5.10.2"))
    testImplementation("org.junit.jupiter:junit-jupiter")
}

tasks.test {
    useJUnitPlatform()
}

tasks.register<Jar>("fatJar") {
    from(sourceSets.main.get().output)
    archiveBaseName.set("AetherDungeons")
    archiveVersion.set(project.version.toString())
    destinationDirectory.set(layout.projectDirectory.dir("releases"))
}

tasks.jar {
    // default jar; fatJar produces the release artifact
    archiveBaseName.set("AetherDungeons-core")
    archiveVersion.set(project.version.toString())
}
