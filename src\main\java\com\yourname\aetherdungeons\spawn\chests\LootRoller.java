package com.yourname.aetherdungeons.spawn.chests;

import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.Random;

public class LootRoller {
    private static final Random RNG = new Random();

    public static void fillInventory(LootTable table, Inventory inv) {
        int rolls = table.getRollsMin() + RNG.nextInt(Math.max(1, table.getRollsMax() - table.getRollsMin() + 1));
        for (int i = 0; i < rolls; i++) {
            LootEntry e = pick(table);
            if (e == null) continue;
            int count = e.min + RNG.nextInt(Math.max(1, e.max - e.min + 1));
            ItemStack stack = new ItemStack(e.item, Math.max(1, Math.min(64, count)));
            inv.addItem(stack);
        }
    }

    private static LootEntry pick(LootTable t) {
        int total = t.totalWeight();
        if (total <= 0) return null;
        int r = RNG.nextInt(total);
        int cur = 0;
        for (LootEntry e : t.getEntries()) {
            cur += e.weight;
            if (r < cur) return e;
        }
        return null;
    }
}
