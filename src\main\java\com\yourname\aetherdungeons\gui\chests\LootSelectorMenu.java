package com.yourname.aetherdungeons.gui.chests;

import com.yourname.aetherdungeons.AetherDungeonsPlugin;
import com.yourname.aetherdungeons.dungeon.model.Dungeon;
import com.yourname.aetherdungeons.spawn.chests.LootStorage;
import com.yourname.aetherdungeons.util.ItemBuilder;
import com.yourname.aetherdungeons.util.Keys;
import com.yourname.aetherdungeons.util.Menu;
import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.List;

public class LootSelectorMenu extends Menu {

    private final AetherDungeonsPlugin plugin;
    private final Dungeon dungeon;

    public LootSelectorMenu(AetherDungeonsPlugin plugin, Dungeon dungeon) {
        super("Select Loot Table", 54);
        this.plugin = plugin;
        this.dungeon = dungeon;
    }

    @Override
    protected void build() {
        inv.clear();
        fillBorders(Material.BROWN_STAINED_GLASS_PANE);
        LootStorage ls = plugin.getLootStorage();
        int i = 10;
        for (String id : ls.getAllIds()) {
            inv.setItem(i, new ItemBuilder(Material.CHEST).name(id).lore(List.of(Component.text("Click to get Chest Placer"))).build());
            i++;
            if (i % 9 == 8) i += 2;
            if (i >= inv.getSize() - 9) break;
        }
        inv.setItem(49, new ItemBuilder(Material.ARROW).name("Back").build());
    }

    @Override
    public void onClick(InventoryClickEvent e) {
        Player p = (Player) e.getWhoClicked();
        int slot = e.getRawSlot();
        if (slot == 49) {
            new com.yourname.aetherdungeons.gui.dungeons.DungeonEditorMenu(plugin, plugin.getDungeonStorage(), dungeon).open(p);
            return;
        }
        var item = e.getCurrentItem();
        if (item == null) return;
        String name = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(item.getItemMeta().displayName());
        var placer = new ItemBuilder(Material.CHEST).name("Chest Placer: " + name).build();
        ItemMeta meta = placer.getItemMeta();
        meta.getPersistentDataContainer().set(Keys.PLACER_TYPE, PersistentDataType.STRING, "loot");
        meta.getPersistentDataContainer().set(Keys.PLACER_DATA, PersistentDataType.STRING, name);
        meta.getPersistentDataContainer().set(Keys.DUNGEON_ID, PersistentDataType.STRING, dungeon.getId());
        placer.setItemMeta(meta);
        p.getInventory().addItem(placer);
        p.closeInventory();
        p.sendMessage(Component.text("Given Chest Placer for loot table '" + name + "'. Place chests in author world."));
    }
}
