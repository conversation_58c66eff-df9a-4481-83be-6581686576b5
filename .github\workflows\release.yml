name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: '21'
          cache: gradle

      - name: Make gradlew executable
        run: chmod +x gradlew

      - name: Build release jar
        run: ./gradlew --no-daemon clean fatJar

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          files: releases/*.jar
          generate_release_notes: true
