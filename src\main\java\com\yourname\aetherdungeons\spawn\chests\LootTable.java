package com.yourname.aetherdungeons.spawn.chests;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class LootTable {
    private String id;
    private int rollsMin = 1;
    private int rollsMax = 2;
    private boolean oneTimePerRun = true;
    private final List<LootEntry> entries = new ArrayList<>();

    public LootTable(String id) { this.id = id; }

    public String getId() { return id; }
    public int getRollsMin() { return rollsMin; }
    public void setRollsMin(int rollsMin) { this.rollsMin = rollsMin; }
    public int getRollsMax() { return rollsMax; }
    public void setRollsMax(int rollsMax) { this.rollsMax = rollsMax; }
    public boolean isOneTimePerRun() { return oneTimePerRun; }
    public void setOneTimePerRun(boolean oneTimePerRun) { this.oneTimePerRun = oneTimePerRun; }
    public List<LootEntry> getEntries() { return entries; }

    public void addEntry(LootEntry e) { entries.add(e); }

    public int totalWeight() {
        return entries.stream().mapToInt(e -> e.weight).sum();
    }
}
